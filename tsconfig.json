{
  "compilerOptions": {
    "target": "ES2020",
    "module": "NodeNext",              // 👈 handles .js/.ts correctly for ESM
    "moduleResolution": "NodeNext",    // 👈 resolves extensions properly
    "rootDir": ".",                    
    "outDir": "./dist",
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src", "config"],
  "exclude": ["node_modules", "dist"]
}
