#!/bin/bash

# Rishi Core - Build and Run Script
# This script lints, builds and runs the Rishi Core application
# Usage: script/run.sh [--skip-lint] [--lint-only] [--lint-fix]

set -e  # Exit on any error

# Change to project root directory (parent of script directory)
cd "$(dirname "$0")/.."

# Parse command line arguments
SKIP_LINT=false
LINT_ONLY=false
LINT_FIX=false

for arg in "$@"; do
    case $arg in
        --skip-lint)
            SKIP_LINT=true
            shift
            ;;
        --lint-only)
            LINT_ONLY=true
            shift
            ;;
        --lint-fix)
            LINT_FIX=true
            shift
            ;;
        *)
            echo "Unknown option: $arg"
            echo "Usage: script/run.sh [--skip-lint] [--lint-only] [--lint-fix]"
            exit 1
            ;;
    esac
done

# Load nvm and use the correct Node.js version
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# Use Node.js version from .nvmrc if available
if [ -f ".nvmrc" ]; then
    echo "📦 Using Node.js version from .nvmrc..."
    nvm use
    echo "✅ Node.js $(node --version) is active"
fi

echo "🚀 Rishi Core Development Script"

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "❌ Dependencies not found. Please run script/setup.sh first."
    exit 1
fi

# Handle lint-fix option
if [ "$LINT_FIX" = true ]; then
    echo "🔧 Running ESLint with auto-fix..."
    npm run lint:fix
    echo "🎨 Running Prettier formatting..."
    npm run format
    echo "✅ Code formatting completed!"
    exit 0
fi

# Handle lint-only option
if [ "$LINT_ONLY" = true ]; then
    echo "🔍 Running ESLint check..."
    npm run lint
    echo "✅ Linting completed!"
    exit 0
fi

# Run linting unless skipped
if [ "$SKIP_LINT" = false ]; then
    echo "🔍 Running ESLint check..."
    if npm run lint; then
        echo "✅ Linting passed!"
    else
        echo "❌ Linting failed. Run './dev run --lint-fix ' to auto-fix issues."
        exit 1
    fi
fi

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist/*

# Build the project
echo "🔨 Building project..."
echo "📦 Compiling TypeScript..."
npm run build

# Check if build was successful
if [ -f "dist/src/App.js" ]; then
    echo "✅ Build completed successfully!"
    echo "📁 Output files:"
    ls -la dist/
else
    echo "❌ Build failed - no output files found"
    exit 1
fi

echo ""
echo "▶️  Running Rishi Core..."
echo "----------------------------------------"

# Run the application
npm start

echo "----------------------------------------"
echo "✅ Application finished"
